from django.test import TestCase
from django.contrib.admin.sites import AdminSite
from django.contrib.auth.models import User
from django.http import HttpRequest
from unittest.mock import patch, MagicMock
from shop.admin import PurchaseAdmin, PurchaseItemAdmin, CommandJobAdmin
from shop.models import Purchase, PurchaseItem, Item, Category, MinecraftServer, CommandJob


class MockRequest:
    def __init__(self, user):
        self.user = user


class AdminActionsTestCase(TestCase):
    def setUp(self):
        # Create test data
        self.server = MinecraftServer.objects.create(
            name='test_server',
            display_name='Test Server',
            ip='127.0.0.1',
            port=25565
        )
        
        self.category = Category.objects.create(
            name='test_category',
            display_name='Test Category'
        )
        
        self.item = Item.objects.create(
            name='test_item',
            display_name='Test Item',
            category=self.category,
            price=100,
            minecraft_server=self.server,
            commands='give {username} diamond 1'
        )
        
        self.purchase = Purchase.objects.create(
            minecraft_username='testuser',
            state=Purchase.State.COMMAND_FAILED
        )
        
        self.purchase_item = PurchaseItem.objects.create(
            purchase=self.purchase,
            item=self.item,
            quantity=1
        )
        
        # Create failed command jobs
        self.failed_job1 = CommandJob.objects.create(
            purchase_item=self.purchase_item,
            command_text='give testuser diamond 1',
            sequence_order=1,
            state=CommandJob.State.FAILED,
            error_message='Test error'
        )
        
        self.failed_job2 = CommandJob.objects.create(
            purchase_item=self.purchase_item,
            command_text='give testuser emerald 1',
            sequence_order=2,
            state=CommandJob.State.FAILED,
            error_message='Test error 2'
        )
        
        # Create successful command job (should not be affected)
        self.successful_job = CommandJob.objects.create(
            purchase_item=self.purchase_item,
            command_text='give testuser gold 1',
            sequence_order=3,
            state=CommandJob.State.SUCCESSFUL
        )
        
        # Create admin instances
        self.site = AdminSite()
        self.purchase_admin = PurchaseAdmin(Purchase, self.site)
        self.purchase_item_admin = PurchaseItemAdmin(PurchaseItem, self.site)
        self.command_job_admin = CommandJobAdmin(CommandJob, self.site)
        
        # Create mock user and request
        self.user = User.objects.create_user('admin', '<EMAIL>', 'password')
        self.request = MockRequest(self.user)

    @patch('shop.admin.async_task')
    def test_purchase_admin_retry_failed_commands(self, mock_async_task):
        """Test retry failed commands action in PurchaseAdmin"""
        queryset = Purchase.objects.filter(id=self.purchase.id)
        
        # Mock the message_user method
        self.purchase_admin.message_user = MagicMock()
        
        # Execute the action
        self.purchase_admin.retry_failed_commands(self.request, queryset)
        
        # Verify failed jobs were reset to pending
        self.failed_job1.refresh_from_db()
        self.failed_job2.refresh_from_db()
        self.successful_job.refresh_from_db()
        
        self.assertEqual(self.failed_job1.state, CommandJob.State.PENDING)
        self.assertEqual(self.failed_job2.state, CommandJob.State.PENDING)
        self.assertEqual(self.successful_job.state, CommandJob.State.SUCCESSFUL)  # Should not change
        
        # Verify error messages were cleared
        self.assertIsNone(self.failed_job1.error_message)
        self.assertIsNone(self.failed_job2.error_message)
        
        # Verify async task was called
        mock_async_task.assert_called_once_with('shop.tasks.execute_purchase_commands', self.purchase.id)
        
        # Verify success message was shown
        self.purchase_admin.message_user.assert_called()

    @patch('shop.admin.async_task')
    def test_purchase_item_admin_retry_failed_commands(self, mock_async_task):
        """Test retry failed commands action in PurchaseItemAdmin"""
        queryset = PurchaseItem.objects.filter(id=self.purchase_item.id)
        
        # Mock the message_user method
        self.purchase_item_admin.message_user = MagicMock()
        
        # Execute the action
        self.purchase_item_admin.retry_failed_commands(self.request, queryset)
        
        # Verify failed jobs were reset to pending
        self.failed_job1.refresh_from_db()
        self.failed_job2.refresh_from_db()
        
        self.assertEqual(self.failed_job1.state, CommandJob.State.PENDING)
        self.assertEqual(self.failed_job2.state, CommandJob.State.PENDING)
        
        # Verify async task was called
        mock_async_task.assert_called_once_with('shop.tasks.execute_purchase_item_commands', self.purchase_item.id)

    @patch('shop.admin.async_task')
    def test_command_job_admin_retry_failed_commands(self, mock_async_task):
        """Test retry failed commands action in CommandJobAdmin"""
        queryset = CommandJob.objects.filter(id__in=[self.failed_job1.id, self.successful_job.id])

        # Mock the message_user method
        self.command_job_admin.message_user = MagicMock()

        # Execute the action
        self.command_job_admin.retry_failed_commands(self.request, queryset)

        # Verify only failed job was reset to pending
        self.failed_job1.refresh_from_db()
        self.successful_job.refresh_from_db()

        self.assertEqual(self.failed_job1.state, CommandJob.State.PENDING)
        self.assertEqual(self.successful_job.state, CommandJob.State.SUCCESSFUL)  # Should not change

        # Verify async task was called only for the failed job
        mock_async_task.assert_called_with('shop.tasks.execute_single_command', self.failed_job1.id)
        self.assertEqual(mock_async_task.call_count, 1)

    @patch('shop.admin.async_task')
    def test_purchase_admin_no_failed_commands(self, mock_async_task):
        """Test retry action when purchase has no failed commands"""
        # Create a purchase with only successful commands
        purchase2 = Purchase.objects.create(
            minecraft_username='testuser2',
            state=Purchase.State.FINISHED
        )
        
        queryset = Purchase.objects.filter(id=purchase2.id)
        
        # Mock the message_user method
        self.purchase_admin.message_user = MagicMock()
        
        # Execute the action
        self.purchase_admin.retry_failed_commands(self.request, queryset)
        
        # Verify async task was not called
        mock_async_task.assert_not_called()
        
        # Verify warning message was shown
        self.purchase_admin.message_user.assert_called()

    @patch('shop.admin.async_task')
    def test_command_job_admin_retry_only_failed_commands(self, mock_async_task):
        """Test retry action with only failed command jobs"""
        queryset = CommandJob.objects.filter(state=CommandJob.State.FAILED)

        # Mock the message_user method
        self.command_job_admin.message_user = MagicMock()

        # Execute the action
        self.command_job_admin.retry_failed_commands(self.request, queryset)

        # Verify failed jobs were reset to pending
        self.failed_job1.refresh_from_db()
        self.failed_job2.refresh_from_db()

        self.assertEqual(self.failed_job1.state, CommandJob.State.PENDING)
        self.assertEqual(self.failed_job2.state, CommandJob.State.PENDING)

        # Verify async task was called for both failed jobs
        self.assertEqual(mock_async_task.call_count, 2)
        mock_async_task.assert_any_call('shop.tasks.execute_single_command', self.failed_job1.id)
        mock_async_task.assert_any_call('shop.tasks.execute_single_command', self.failed_job2.id)
